<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NAT64 测速工具 - 本地测试</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; margin: 2em; background-color: #f8f9fa; color: #343a40; }
    .container { max-width: 1024px; margin: auto; }
    h1 { color: #007bff; }
    button { font-size: 1rem; padding: 10px 15px; border-radius: 5px; border: none; cursor: pointer; background-color: #007bff; color: white; transition: background-color 0.2s; }
    button:disabled { background-color: #6c757d; cursor: not-allowed; }
    button:hover:not(:disabled) { background-color: #0056b3; }
    #status { margin-top: 20px; font-style: italic; color: #6c757d; }
    table { width: 100%; border-collapse: collapse; margin-top: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    th, td { padding: 12px 15px; text-align: left; border-bottom: 1px solid #dee2e6; font-size: 0.9rem; }
    thead { background-color: #343a40; color: white; }
    tbody tr:nth-child(even) { background-color: #f2f2f2; }
    code { background-color: #e9ecef; padding: 2px 5px; border-radius: 4px; font-family: 'Courier New', Courier, monospace; }
    .test-section { margin: 20px 0; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
  </style>
</head>
<body>
  <div class="container">
    <h1>NAT64 网络测速工具 - 本地测试</h1>
    <p>这是一个本地测试页面，用于验证数据解析和前端逻辑。</p>
    
    <div class="test-section">
      <h2>数据解析测试</h2>
      <button id="test-parse-btn">测试数据解析</button>
      <div id="parse-results"></div>
    </div>
    
    <div class="test-section">
      <h2>前端界面测试</h2>
      <button id="start-btn" disabled>开始测速</button>
      <div id="status">正在初始化...</div>
      <table id="results-table" style="display:none;">
        <thead>
          <tr>
            <th>供应商</th>
            <th>国家/地区</th>
            <th>NAT64 前缀</th>
            <th>状态</th>
            <th>平均延迟</th>
            <th>总延迟</th>
            <th>下载速度</th>
          </tr>
        </thead>
        <tbody id="results-body"></tbody>
      </table>
    </div>
  </div>

  <script>
    // 模拟新的数据结构
    const mockProvidersData = {
      "Kasper Dupont": {
        "Germany / Nürnberg": ["2a00:1098:2b::/96", "2a00:1098:2c:1::/96", "2a01:4f8:c2c:123f:64::/96", "2a01:4f9:c010:3f02:64::/96"],
        "United Kingdom / London": ["2a00:1098:2b::/96", "2a00:1098:2c:1::/96", "2a01:4f8:c2c:123f:64::/96", "2a01:4f9:c010:3f02:64::/96"],
        "Finland / Helsinki": ["2a00:1098:2b::/96", "2a00:1098:2c:1::/96", "2a01:4f8:c2c:123f:64::/96", "2a01:4f9:c010:3f02:64::/96"]
      },
      "level66.services": {
        "Germany / Anycast": ["2001:67c:2960:6464::/96"]
      },
      "Trex": {
        "Finland / Tampere": ["2001:67c:2b0:db32:0:1::/96"]
      },
      "ZTVI": {
        "U.S.A. / Fremont": ["2602:fc59:b0:64::/96"],
        "U.S.A. / Chicago": ["2602:fc59:11:64::/96"]
      }
    };

    const startBtn = document.getElementById('start-btn');
    const statusDiv = document.getElementById('status');
    const resultsTable = document.getElementById('results-table');
    const resultsBody = document.getElementById('results-body');
    const testParseBtn = document.getElementById('test-parse-btn');
    const parseResults = document.getElementById('parse-results');
    
    let providersData = [];

    // 测试数据解析
    testParseBtn.addEventListener('click', () => {
      parseResults.innerHTML = '<h3>解析结果:</h3>';

      let totalPrefixes = 0;
      let providerIndex = 1;

      for (const [provider, regions] of Object.entries(mockProvidersData)) {
        parseResults.innerHTML += `<div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;">`;
        parseResults.innerHTML += `<strong>${providerIndex}. ${provider}</strong><br>`;

        let regionIndex = 1;
        for (const [region, prefixes] of Object.entries(regions)) {
          parseResults.innerHTML += `&nbsp;&nbsp;${regionIndex}. 地区: ${region}<br>`;
          parseResults.innerHTML += `&nbsp;&nbsp;&nbsp;&nbsp;前缀数量: ${prefixes.length}<br>`;
          parseResults.innerHTML += `&nbsp;&nbsp;&nbsp;&nbsp;前缀列表: ${prefixes.join(', ')}<br>`;
          totalPrefixes += prefixes.length;
          regionIndex++;
        }
        parseResults.innerHTML += `</div>`;
        providerIndex++;
      }

      const providerCount = Object.keys(mockProvidersData).length;
      parseResults.innerHTML += `<p><strong>总计: ${providerCount} 个提供商，${totalPrefixes} 个前缀</strong></p>`;
    });

    // 模拟加载提供商列表
    function loadProviders() {
      statusDiv.textContent = '正在加载模拟数据...';
      resultsTable.style.display = 'table';
      resultsBody.innerHTML = '<tr><td colspan="7"><i>正在获取提供商列表...</i></td></tr>';
      
      setTimeout(() => {
        providersData = mockProvidersData;
        displayProviders();

        // 计算总的提供商数量和前缀数量
        const providerCount = Object.keys(providersData).length;
        let totalPrefixes = 0;
        for (const regions of Object.values(providersData)) {
          for (const prefixes of Object.values(regions)) {
            totalPrefixes += prefixes.length;
          }
        }

        statusDiv.textContent = `已加载 ${providerCount} 个提供商，共 ${totalPrefixes} 个前缀，点击"开始测速"进行模拟测试`;
        startBtn.disabled = false;
      }, 1000);
    }
    
    // 显示提供商列表
    function displayProviders() {
      resultsBody.innerHTML = '';

      // 遍历新的数据结构：{供应商: {地区: [前缀数组]}}
      for (const [provider, regions] of Object.entries(providersData)) {
        for (const [region, prefixes] of Object.entries(regions)) {
          // 为每个前缀创建一行
          for (const prefix of prefixes) {
            const row = resultsBody.insertRow();
            row.innerHTML = `
              <td>${provider}</td>
              <td>${region}</td>
              <td><code>${prefix}</code></td>
              <td>等待测试</td>
              <td>-</td>
              <td>-</td>
              <td>-</td>
            `;
          }
        }
      }
    }

    // 模拟测速
    startBtn.addEventListener('click', async () => {
      startBtn.disabled = true;
      startBtn.textContent = '测速中...';
      
      const allPrefixes = [];
      for (const [provider, regions] of Object.entries(providersData)) {
        for (const [region, prefixes] of Object.entries(regions)) {
          for (const prefix of prefixes) {
            allPrefixes.push({ provider, region, prefix });
          }
        }
      }
      
      let completedCount = 0;
      const totalCount = allPrefixes.length;
      const rows = resultsBody.querySelectorAll('tr');
      
      // 重置状态
      rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 4) {
          cells[3].textContent = '测试中...';
          cells[3].style.color = '#6c757d';
        }
      });
      
      // 模拟异步测试
      for (let i = 0; i < allPrefixes.length; i++) {
        const item = allPrefixes[i];
        const row = rows[i];
        const cells = row.querySelectorAll('td');
        
        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500));
        
        // 模拟测试结果
        const success = Math.random() > 0.2; // 80% 成功率
        
        if (success) {
          cells[3].textContent = '成功';
          cells[3].style.color = 'green';
          cells[4].textContent = Math.floor(Math.random() * 100 + 20) + ' ms';
          cells[5].textContent = Math.floor(Math.random() * 300 + 60) + ' ms';
          cells[6].textContent = (Math.random() * 50 + 10).toFixed(2) + ' Mbps';
        } else {
          cells[3].textContent = '失败';
          cells[3].style.color = 'red';
          cells[4].textContent = 'N/A';
          cells[5].textContent = 'N/A';
          cells[6].textContent = 'N/A';
        }
        
        completedCount++;
        statusDiv.textContent = `测速进度: ${completedCount}/${totalCount} (${Math.round(completedCount/totalCount*100)}%)`;
      }
      
      statusDiv.textContent = `测速完成！共测试了 ${totalCount} 个 NAT64 前缀`;
      startBtn.disabled = false;
      startBtn.textContent = '重新测速';
    });
    
    // 页面加载时自动执行
    window.addEventListener('load', loadProviders);
  </script>
</body>
</html>
