// 导入 Cloudflare Sockets API，用于建立底层TCP连接
import { connect } from 'cloudflare:sockets';

// --- 后端配置项 ---
const TEST_IPV4 = '*******'; // 用于延迟和下载测试的目标IPv4地址
const LATENCY_TEST_COUNT = 3; // 对每个前缀进行延迟测试的次数
const DOWNLOAD_TARGET_HOST = 'speed.cloudflare.com';
const DOWNLOAD_TARGET_BYTES = 1000000; // 下载 1MB 数据
const DOWNLOAD_TARGET_PATH = `/__down?bytes=${DOWNLOAD_TARGET_BYTES}`;

export default {
  async fetch(request) {
    const url = new URL(request.url);

    // 根据请求路径进行路由
    switch (url.pathname) {
      case '/':
        // 访问根目录时，返回带有前端逻辑的HTML页面
        return serveHtmlPage();

      case '/api/prefixes':
        // API端点：获取NAT64前缀列表
        try {
          const prefixes = await getNat64ProvidersList();
          return new Response(JSON.stringify(prefixes), {
            headers: { 'Content-Type': 'application/json' },
          });
        } catch (err) {
          return new Response(JSON.stringify({ error: err.message }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          });
        }

      case '/api/test':
        // API端点：对单个前缀进行测速
        const prefix = url.searchParams.get('prefix');
        if (!prefix || !prefix.includes('::/96')) {
          return new Response(JSON.stringify({ error: '无效或缺少 "prefix" 参数' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          });
        }
        try {
          const result = await testNat64Provider(prefix);
          return new Response(JSON.stringify(result), {
            headers: { 'Content-Type': 'application/json' },
          });
        } catch (err) {
           return new Response(JSON.stringify({ error: err.message }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          });
        }

      default:
        return new Response('路径未找到', { status: 404 });
    }
  },
};

// =================================================================
// ===== 后端逻辑：单个提供商的测试函数 (这部分无变化)
// =================================================================

async function testNat64Provider(prefix) {
  try {
    const [latencyResult, downloadResult] = await Promise.all([
      testLatency(prefix),
      testDownloadSpeed(prefix)
    ]);
    return { prefix, ...latencyResult, ...downloadResult, status: 'OK' };
  } catch (error) {
    return { prefix, status: '失败', error: error.message, avgLatency: Infinity, totalLatency: Infinity, downloadSpeed: 0 };
  }
}
async function testLatency(prefix) {
  const latencies = [];
  let totalLatency = 0;
  const targetAddress = constructNat64Address(prefix, TEST_IPV4);
  for (let i = 0; i < LATENCY_TEST_COUNT; i++) {
    const startTime = Date.now();
    try {
      const socket = await connect({ hostname: targetAddress, port: 443 });
      await socket.close();
      const endTime = Date.now();
      latencies.push(endTime - startTime);
    } catch (e) {
      latencies.push(Infinity);
      break; 
    }
  }
  totalLatency = latencies.reduce((a, b) => a + b, 0);
  const successfulPings = latencies.filter(l => l !== Infinity);
  if (successfulPings.length === 0) return { avgLatency: Infinity, totalLatency: Infinity };
  return { avgLatency: totalLatency / successfulPings.length, totalLatency };
}
async function testDownloadSpeed(prefix) {
  try {
    const targetAddress = constructNat64Address(prefix, TEST_IPV4);
    const socket = await connect({ hostname: targetAddress, port: 443, secureTransport: "on" });
    const writer = socket.writable.getWriter();
    const httpRequest = `GET ${DOWNLOAD_TARGET_PATH} HTTP/1.1\r\nHost: ${DOWNLOAD_TARGET_HOST}\r\nConnection: close\r\n\r\n`;
    await writer.write(new TextEncoder().encode(httpRequest));
    const reader = socket.readable.getReader();
    let receivedBytes = 0;
    const startTime = Date.now();
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      receivedBytes += value.length;
    }
    const endTime = Date.now();
    const durationInSeconds = (endTime - startTime) / 1000;
    if (durationInSeconds < 0.1 || receivedBytes < DOWNLOAD_TARGET_BYTES * 0.9) return { downloadSpeed: 0 };
    return { downloadSpeed: (receivedBytes * 8) / durationInSeconds / 1000 / 1000 };
  } catch (err) {
    return { downloadSpeed: 0 };
  }
}

// =================================================================
// ===== 后端逻辑：HTML页面抓取和地址构造 (重大改进)
// =================================================================

/**
 * 抓取并解析 nat64.xyz 页面的完整提供商信息。
 * @returns {Promise<Array<[string, string, string]>>} - 返回一个包含 [供应商, 国家/地区, 前缀] 的数组
 */
async function getNat64ProvidersList() {
  const response = await fetch("https://nat64.xyz/", {
    headers: { 'User-Agent': 'Cloudflare-Worker-Nat64-Speed-Test' }
  });
  if (!response.ok) {
    throw new Error(`无法获取 nat64.xyz 页面: ${response.statusText}`);
  }

  const results = [];
  let currentRow = {};

  const rewriter = new HTMLRewriter()
    .on("tbody > tr", {
      element(element) {
        // 每当遇到一个新行时，重置当前行的数据对象
        currentRow = { provider: '', country: '', prefixes: '' };
      },
      text(text) {
        // 当一行处理完毕后，根据收集到的数据生成最终的条目
        if (currentRow.provider && currentRow.country && currentRow.prefixes) {
          const prefixes = currentRow.prefixes.split('<br>').map(p => p.trim()).filter(p => p.includes('::/96'));
          prefixes.forEach(prefix => {
            results.push([currentRow.provider, currentRow.country, prefix]);
          });
        }
      }
    })
    .on("tbody > tr > td:nth-child(1)", {
      text(text) { currentRow.provider += text.text; }
    })
    .on("tbody > tr > td:nth-child(2)", {
      text(text) { currentRow.country += text.text; }
    })
    .on("tbody > tr > td:nth-child(4)", {
      // 特别处理包含 <br> 标签的单元格
      text(chunk) {
        currentRow.prefixes += chunk.text;
        if (!chunk.lastInTextNode) {
          // 如果文本节点后面还有内容（比如<br>），我们手动添加一个分隔符
          currentRow.prefixes += "<br>";
        }
      }
    });

  await rewriter.transform(response).text();

  return results;
}

function constructNat64Address(prefix, ipv4) {
    const cleanedPrefix = prefix.replace(/::\/96$/, '::');
    const parts = ipv4.split('.');
    const hex = parts.map(part => parseInt(part, 10).toString(16).padStart(2, '0'));
    return `[${cleanedPrefix}${hex[0]}${hex[1]}:${hex[2]}${hex[3]}]`;
}


// =================================================================
// ===== 前端页面生成 (重大改进)
// =================================================================

function serveHtmlPage() {
  const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NAT64 网络测速工具</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; margin: 2em; background-color: #f8f9fa; color: #343a40; }
    .container { max-width: 1024px; margin: auto; }
    h1 { color: #007bff; }
    button { font-size: 1rem; padding: 10px 15px; border-radius: 5px; border: none; cursor: pointer; background-color: #007bff; color: white; transition: background-color 0.2s; }
    button:disabled { background-color: #6c757d; cursor: not-allowed; }
    button:hover:not(:disabled) { background-color: #0056b3; }
    #status { margin-top: 20px; font-style: italic; color: #6c757d; }
    table { width: 100%; border-collapse: collapse; margin-top: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    th, td { padding: 12px 15px; text-align: left; border-bottom: 1px solid #dee2e6; font-size: 0.9rem; }
    thead { background-color: #343a40; color: white; }
    tbody tr:nth-child(even) { background-color: #f2f2f2; }
    code { background-color: #e9ecef; padding: 2px 5px; border-radius: 4px; font-family: 'Courier New', Courier, monospace; }
  </style>
</head>
<body>
  <div class="container">
    <h1>NAT64 网络测速工具</h1>
    <p>该工具将从 Cloudflare Worker 对 <a href="https://nat64.xyz/" target="_blank">nat64.xyz</a> 上列出的所有 NAT64 服务进行延迟和下载速度测试。</p>
    <button id="start-btn">开始测速</button>
    <div id="status"></div>
    <table id="results-table" style="display:none;">
      <thead>
        <tr>
          <th>供应商</th>
          <th>国家/地区</th>
          <th>NAT64 前缀</th>
          <th>状态</th>
          <th>平均延迟</th>
          <th>总延迟 (${LATENCY_TEST_COUNT}次)</th>
          <th>下载速度 (1MB)</th>
        </tr>
      </thead>
      <tbody id="results-body"></tbody>
    </table>
  </div>

  <script>
    const startBtn = document.getElementById('start-btn');
    const statusDiv = document.getElementById('status');
    const resultsTable = document.getElementById('results-table');
    const resultsBody = document.getElementById('results-body');
    
    startBtn.addEventListener('click', async () => {
      startBtn.disabled = true;
      statusDiv.textContent = '正在从 /api/prefixes 获取 nat64.xyz 列表...';
      resultsTable.style.display = 'table';
      resultsBody.innerHTML = '<tr><td colspan="7"><i>正在获取提供商列表...</i></td></tr>';
      
      let providers = [];
      try {
        const response = await fetch('/api/prefixes');
        if (!response.ok) throw new Error('获取前缀列表失败: ' + response.statusText);
        providers = await response.json();
        if(providers.length === 0) throw new Error('未能解析到任何提供商信息。');
      } catch (err) {
        statusDiv.textContent = '错误: ' + err.message;
        startBtn.disabled = false;
        resultsBody.innerHTML = \`<tr><td colspan="7" style="color:red;">\${err.message}</td></tr>\`;
        return;
      }
      
      resultsBody.innerHTML = ''; // 清空表格准备填充数据
      let count = 0;
      for (const providerInfo of providers) {
        const [provider, country, prefix] = providerInfo;
        count++;
        statusDiv.textContent = \`正在测试第 \${count} / \${providers.length} 个: \${prefix}\`;
        
        // 为当前测试在表格中创建一行
        const row = resultsBody.insertRow();
        row.innerHTML = \`
          <td>\${provider}</td>
          <td>\${country}</td>
          <td><code>\${prefix}</code></td>
          <td colspan="4"><i>正在测试中...</i></td>
        \`;
        
        try {
          const response = await fetch(\`/api/test?prefix=\${encodeURIComponent(prefix)}\`);
          const result = await response.json();

          if (result.error) throw new Error(result.error);
          
          const statusCell = result.status === 'OK' ? '<td style="color:green;">成功</td>' : '<td style="color:red;">失败</td>';
          const avgLatencyCell = \`<td>\${result.avgLatency === Infinity ? 'N/A' : result.avgLatency.toFixed(0) + ' ms'}</td>\`;
          const totalLatencyCell = \`<td>\${result.totalLatency === Infinity ? 'N/A' : result.totalLatency + ' ms'}</td>\`;
          const speedCell = \`<td>\${result.downloadSpeed > 0 ? result.downloadSpeed.toFixed(2) + ' Mbps' : 'N/A'}</td>\`;
          
          row.innerHTML = \`
            <td>\${provider}</td>
            <td>\${country}</td>
            <td><code>\${prefix}</code></td>
            \${statusCell}\${avgLatencyCell}\${totalLatencyCell}\${speedCell}
          \`;
        } catch (err) {
          row.innerHTML = \`
            <td>\${provider}</td>
            <td>\${country}</td>
            <td><code>\${prefix}</code></td>
            <td style="color:red;">客户端错误</td>
            <td colspan="3">\${err.message}</td>
          \`;
        }
      }
      statusDiv.textContent = '所有测试已完成！';
      startBtn.disabled = false; // 允许重新开始
    });
  </script>
</body>
</html>
  `;
  return new Response(html, {
    headers: { 'Content-Type': 'text/html; charset=utf-8' },
  });
}