<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>下载测试修复验证</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
    .container { max-width: 800px; margin: auto; }
    .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    h1, h2 { color: #333; }
    button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
    .btn-primary { background: #007bff; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: black; }
    button:disabled { background: #6c757d; cursor: not-allowed; }
    .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
    .status { margin: 10px 0; padding: 10px; border-radius: 4px; }
    .status.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    .status.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    .status.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    .config { background: #e9ecef; padding: 10px; border-radius: 4px; margin: 10px 0; }
  </style>
</head>
<body>
  <div class="container">
    <h1>下载测试修复验证</h1>
    
    <div class="test-section">
      <h2>新配置信息</h2>
      <div class="config">
        <strong>旧配置（有问题）：</strong><br>
        - 目标：speed.cloudflare.com<br>
        - IP：************<br>
        - 路径：/__down?bytes=100000<br>
        - 问题：不支持直接IP访问<br><br>
        
        <strong>新配置（修复后）：</strong><br>
        - 目标：httpbin.org<br>
        - IP：**************<br>
        - 路径：/bytes/50000<br>
        - 优势：支持直接IP访问，返回纯二进制数据
      </div>
    </div>
    
    <div class="test-section">
      <h2>1. 测试 httpbin.org 可访问性</h2>
      <p>验证 httpbin.org 是否支持直接IP访问</p>
      <button id="test-accessibility" class="btn-primary">测试可访问性</button>
      <div id="accessibility-status" class="status info">点击按钮开始测试</div>
      <div id="accessibility-log" class="log"></div>
    </div>
    
    <div class="test-section">
      <h2>2. 模拟下载速度测试</h2>
      <p>模拟新的下载测试逻辑</p>
      <button id="test-download" class="btn-success">模拟下载测试</button>
      <div id="download-status" class="status info">点击按钮开始测试</div>
      <div id="download-log" class="log"></div>
    </div>
    
    <div class="test-section">
      <h2>3. 对比测试结果</h2>
      <p>对比修复前后的测试结果</p>
      <button id="test-compare" class="btn-warning">对比测试</button>
      <div id="compare-status" class="status info">点击按钮开始对比</div>
      <div id="compare-log" class="log"></div>
    </div>
  </div>

  <script>
    function log(elementId, message, includeTime = true) {
      const logElement = document.getElementById(elementId);
      const timestamp = includeTime ? `[${new Date().toLocaleTimeString()}] ` : '';
      logElement.innerHTML += timestamp + message + '\n';
      logElement.scrollTop = logElement.scrollHeight;
    }

    function setStatus(elementId, message, type = 'info') {
      const statusElement = document.getElementById(elementId);
      statusElement.textContent = message;
      statusElement.className = `status ${type}`;
    }

    // 测试1：httpbin.org 可访问性
    document.getElementById('test-accessibility').addEventListener('click', async () => {
      const button = document.getElementById('test-accessibility');
      button.disabled = true;
      
      setStatus('accessibility-status', '正在测试 httpbin.org 可访问性...', 'info');
      log('accessibility-log', '=== httpbin.org 可访问性测试 ===');
      
      try {
        // 测试通过域名访问
        log('accessibility-log', '1. 测试通过域名访问 httpbin.org/bytes/1000');
        const domainResponse = await fetch('https://httpbin.org/bytes/1000');
        log('accessibility-log', `   状态: ${domainResponse.status} ${domainResponse.statusText}`);
        log('accessibility-log', `   内容长度: ${domainResponse.headers.get('content-length')} bytes`);
        
        const domainData = await domainResponse.arrayBuffer();
        log('accessibility-log', `   实际接收: ${domainData.byteLength} bytes`);
        
        // 注意：浏览器无法直接通过IP访问HTTPS，这里只是演示逻辑
        log('accessibility-log', '');
        log('accessibility-log', '2. 直接IP访问测试（仅在Worker环境中可用）');
        log('accessibility-log', '   浏览器环境无法测试直接IP的HTTPS访问');
        log('accessibility-log', '   但Worker环境中应该可以正常工作');
        
        setStatus('accessibility-status', 'httpbin.org 域名访问正常，IP访问需在Worker中验证', 'success');
        
      } catch (error) {
        log('accessibility-log', `错误: ${error.message}`);
        setStatus('accessibility-status', 'httpbin.org 访问测试失败', 'error');
      }
      
      button.disabled = false;
    });

    // 测试2：模拟下载速度测试
    document.getElementById('test-download').addEventListener('click', async () => {
      const button = document.getElementById('test-download');
      button.disabled = true;
      
      setStatus('download-status', '正在模拟下载速度测试...', 'info');
      log('download-log', '=== 模拟下载速度测试 ===');
      
      // 模拟新的下载测试逻辑
      const mockDownloadTest = async (testName, scenario) => {
        log('download-log', `\n测试场景: ${testName}`);
        log('download-log', `配置: ${scenario.config}`);
        
        const startTime = Date.now();
        
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, scenario.delay));
        
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        if (scenario.success) {
          const speed = (scenario.bytes * 8) / duration / 1000 / 1000;
          log('download-log', `✓ 成功: ${scenario.bytes} bytes in ${duration.toFixed(2)}s`);
          log('download-log', `  下载速度: ${speed.toFixed(2)} Mbps`);
          return { downloadSpeed: speed, debugInfo: `${scenario.bytes} bytes in ${duration.toFixed(2)}s` };
        } else {
          log('download-log', `✗ 失败: ${scenario.error}`);
          return { downloadSpeed: 0, debugInfo: scenario.error };
        }
      };
      
      try {
        // 测试不同场景
        const scenarios = [
          {
            name: '正常下载（新配置）',
            config: 'httpbin.org/bytes/50000',
            delay: 800,
            success: true,
            bytes: 50000
          },
          {
            name: '快速下载（新配置）',
            config: 'httpbin.org/bytes/50000',
            delay: 400,
            success: true,
            bytes: 50000
          },
          {
            name: '连接超时（模拟）',
            config: 'httpbin.org/bytes/50000',
            delay: 100,
            success: false,
            error: 'Duration: 0.1s, Content: 0/50000 bytes'
          }
        ];
        
        for (const scenario of scenarios) {
          await mockDownloadTest(scenario.name, scenario);
        }
        
        log('download-log', '\n=== 模拟测试完成 ===');
        setStatus('download-status', '下载速度测试模拟完成', 'success');
        
      } catch (error) {
        log('download-log', `错误: ${error.message}`);
        setStatus('download-status', '下载速度测试模拟失败', 'error');
      }
      
      button.disabled = false;
    });

    // 测试3：对比测试结果
    document.getElementById('test-compare').addEventListener('click', async () => {
      const button = document.getElementById('test-compare');
      button.disabled = true;
      
      setStatus('compare-status', '正在对比修复前后的结果...', 'info');
      log('compare-log', '=== 修复前后对比 ===');
      
      log('compare-log', '修复前的问题:');
      log('compare-log', '- 目标: speed.cloudflare.com');
      log('compare-log', '- 结果: 全部显示 "Debug: Duration: 0s, Bytes: 0"');
      log('compare-log', '- 原因: 不支持直接IP访问，连接被拒绝');
      log('compare-log', '');
      
      log('compare-log', '修复后的改进:');
      log('compare-log', '- 目标: httpbin.org (支持直接IP访问)');
      log('compare-log', '- 数据量: 从100KB减少到50KB (提高测试速度)');
      log('compare-log', '- 解析: 简化HTTP头解析逻辑');
      log('compare-log', '- 验证: 放宽成功条件 (50%数据接收即可)');
      log('compare-log', '');
      
      // 模拟对比结果
      const comparisonData = [
        { prefix: '2a00:1098:2b::/96', before: 'Debug: Duration: 0s, Bytes: 0', after: '25.67 Mbps' },
        { prefix: '2001:67c:2960:6464::/96', before: 'Debug: Duration: 0s, Bytes: 0', after: '18.43 Mbps' },
        { prefix: '2602:fc59:b0:64::/96', before: 'Debug: Duration: 0s, Bytes: 0', after: '31.25 Mbps' }
      ];
      
      log('compare-log', '预期结果对比:');
      comparisonData.forEach((item, index) => {
        log('compare-log', `${index + 1}. ${item.prefix}`);
        log('compare-log', `   修复前: ${item.before}`);
        log('compare-log', `   修复后: ${item.after}`);
        log('compare-log', '');
      });
      
      setStatus('compare-status', '对比完成 - 修复后应该显示实际下载速度', 'success');
      
      button.disabled = false;
    });
  </script>
</body>
</html>
