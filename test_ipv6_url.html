<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>IPv6 URL 格式测试</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
    .container { max-width: 800px; margin: auto; }
    .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    h1, h2 { color: #333; }
    .code { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 4px; font-family: monospace; font-size: 12px; }
    .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; margin: 10px 0; }
    .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
  </style>
</head>
<body>
  <div class="container">
    <h1>IPv6 URL 格式修复验证</h1>
    
    <div class="test-section">
      <h2>问题分析</h2>
      <div class="error">
        <strong>错误信息：</strong><br>
        TypeError - Invalid URL: http://2a00:1098:2c:1::5a82:4649/1MB.zip
      </div>
      
      <div class="success">
        <strong>问题原因：</strong><br>
        IPv6地址在URL中必须用方括号 [] 包围
      </div>
    </div>
    
    <div class="test-section">
      <h2>修复方案</h2>
      
      <div class="code">
        <strong>修复前（错误）：</strong><br>
        const url = `http://${targetAddress.replace(/[\[\]]/g, '')}${target.path}`;<br>
        // 结果: http://2a00:1098:2c:1::5a82:4649/1MB.zip ❌
      </div>
      
      <div class="code">
        <strong>修复后（正确）：</strong><br>
        const cleanAddress = targetAddress.replace(/[\[\]]/g, '');<br>
        const url = `http://[${cleanAddress}]${target.path}`;<br>
        // 结果: http://[2a00:1098:2c:1::5a82:4649]/1MB.zip ✅
      </div>
    </div>
    
    <div class="test-section">
      <h2>测试示例</h2>
      <div id="test-results"></div>
    </div>
  </div>

  <script>
    function constructNat64Address(prefix, ipv4) {
      const cleanedPrefix = prefix.replace(/::\/96$/, '::');
      const parts = ipv4.split('.');
      const hex = parts.map(part => parseInt(part, 10).toString(16).padStart(2, '0'));
      return `[${cleanedPrefix}${hex[0]}${hex[1]}:${hex[2]}${hex[3]}]`;
    }

    // 测试数据
    const testData = [
      { prefix: '2a00:1098:2b::/96', ip: '***********', path: '/1MB.zip' },
      { prefix: '2a00:1098:2c:1::/96', ip: '***********', path: '/1MB.zip' },
      { prefix: '2001:67c:2960:6464::/96', ip: '***********', path: '/1MB.zip' }
    ];

    // 生成测试结果
    const resultsDiv = document.getElementById('test-results');
    let html = '<h3>URL构造测试结果：</h3>';

    testData.forEach((test, index) => {
      const nat64Address = constructNat64Address(test.prefix, test.ip);
      const cleanAddress = nat64Address.replace(/[\[\]]/g, '');
      
      // 错误的URL（修复前）
      const wrongUrl = `http://${cleanAddress}${test.path}`;
      
      // 正确的URL（修复后）
      const correctUrl = `http://[${cleanAddress}]${test.path}`;
      
      html += `
        <div class="code">
          <strong>${index + 1}. 前缀: ${test.prefix}</strong><br>
          NAT64地址: ${nat64Address}<br>
          清理后: ${cleanAddress}<br>
          <span style="color: red;">错误URL: ${wrongUrl}</span><br>
          <span style="color: green;">正确URL: ${correctUrl}</span><br>
        </div>
      `;
    });

    html += `
      <div class="success">
        <strong>修复验证：</strong><br>
        • 所有IPv6地址现在都正确地用方括号包围<br>
        • URL格式符合RFC 3986标准<br>
        • 应该解决 "Invalid URL" 错误
      </div>
    `;

    resultsDiv.innerHTML = html;
  </script>
</body>
</html>
