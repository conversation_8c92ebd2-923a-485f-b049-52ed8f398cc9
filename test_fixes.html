<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NAT64 修复测试</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
    .container { max-width: 1200px; margin: auto; }
    .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    h1, h2 { color: #333; }
    button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
    .btn-primary { background: #007bff; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: black; }
    button:disabled { background: #6c757d; cursor: not-allowed; }
    .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
    .status { margin: 10px 0; padding: 10px; border-radius: 4px; }
    .status.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    .status.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    .status.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #dee2e6; font-size: 12px; }
    thead { background: #343a40; color: white; }
    .timing { color: #666; font-size: 11px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>NAT64 测速修复验证</h1>
    
    <div class="test-section">
      <h2>1. 并发测试验证</h2>
      <p>测试新的并发控制逻辑是否能同时发起所有请求</p>
      <button id="test-concurrent" class="btn-primary">测试并发行为</button>
      <button id="clear-log" class="btn-warning">清空日志</button>
      <div id="concurrent-status" class="status info">点击按钮开始测试</div>
      <div id="concurrent-log" class="log"></div>
    </div>
    
    <div class="test-section">
      <h2>2. 下载速度测试验证</h2>
      <p>测试修复后的下载速度测试功能</p>
      <button id="test-download" class="btn-success">测试下载功能</button>
      <div id="download-status" class="status info">点击按钮开始测试</div>
      <div id="download-log" class="log"></div>
    </div>
    
    <div class="test-section">
      <h2>3. 完整功能测试</h2>
      <p>模拟完整的测速流程</p>
      <button id="test-full" class="btn-primary">完整测试</button>
      <div id="full-status" class="status info">点击按钮开始测试</div>
      <table id="results-table" style="display:none;">
        <thead>
          <tr>
            <th>供应商</th>
            <th>地区</th>
            <th>前缀</th>
            <th>状态</th>
            <th>延迟</th>
            <th>下载速度</th>
            <th>开始时间</th>
          </tr>
        </thead>
        <tbody id="results-body"></tbody>
      </table>
    </div>
  </div>

  <script>
    // 模拟数据
    const testPrefixes = [
      { provider: 'Test1', region: 'Region1', prefix: '2001:db8:1::/96' },
      { provider: 'Test2', region: 'Region2', prefix: '2001:db8:2::/96' },
      { provider: 'Test3', region: 'Region3', prefix: '2001:db8:3::/96' },
      { provider: 'Test4', region: 'Region4', prefix: '2001:db8:4::/96' },
      { provider: 'Test5', region: 'Region5', prefix: '2001:db8:5::/96' },
    ];

    function log(elementId, message, includeTime = true) {
      const logElement = document.getElementById(elementId);
      const timestamp = includeTime ? `[${new Date().toLocaleTimeString()}] ` : '';
      logElement.innerHTML += timestamp + message + '\n';
      logElement.scrollTop = logElement.scrollHeight;
    }

    function setStatus(elementId, message, type = 'info') {
      const statusElement = document.getElementById(elementId);
      statusElement.textContent = message;
      statusElement.className = `status ${type}`;
    }

    // 测试1：并发行为验证
    document.getElementById('test-concurrent').addEventListener('click', async () => {
      const button = document.getElementById('test-concurrent');
      button.disabled = true;
      
      setStatus('concurrent-status', '正在测试并发行为...', 'info');
      log('concurrent-log', '=== 并发测试开始 ===');
      
      // 模拟并发控制器
      class ConcurrencyController {
        constructor(limit) {
          this.limit = limit;
          this.running = 0;
          this.queue = [];
        }
        
        async add(asyncFunction) {
          return new Promise((resolve, reject) => {
            this.queue.push({ asyncFunction, resolve, reject });
            this.tryNext();
          });
        }
        
        async tryNext() {
          if (this.running >= this.limit || this.queue.length === 0) return;
          
          this.running++;
          const { asyncFunction, resolve, reject } = this.queue.shift();
          
          try {
            const result = await asyncFunction();
            resolve(result);
          } catch (error) {
            reject(error);
          } finally {
            this.running--;
            this.tryNext();
          }
        }
      }

      const controller = new ConcurrencyController(3);
      const startTime = Date.now();
      
      // 创建所有任务
      const allPromises = testPrefixes.map((item, index) => {
        return controller.add(async () => {
          const taskStartTime = Date.now() - startTime;
          log('concurrent-log', `任务 ${index + 1} (${item.prefix}) 开始执行 - ${taskStartTime}ms`);
          
          // 模拟网络请求
          await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500));
          
          const taskEndTime = Date.now() - startTime;
          log('concurrent-log', `任务 ${index + 1} (${item.prefix}) 完成 - ${taskEndTime}ms`);
          
          return { index, prefix: item.prefix, duration: taskEndTime - taskStartTime };
        });
      });
      
      log('concurrent-log', `所有 ${testPrefixes.length} 个任务已提交到并发控制器`);
      
      try {
        const results = await Promise.all(allPromises);
        const totalTime = Date.now() - startTime;
        
        log('concurrent-log', `=== 所有任务完成，总耗时: ${totalTime}ms ===`);
        setStatus('concurrent-status', `并发测试完成！总耗时: ${totalTime}ms`, 'success');
      } catch (error) {
        log('concurrent-log', `错误: ${error.message}`);
        setStatus('concurrent-status', '并发测试失败', 'error');
      }
      
      button.disabled = false;
    });

    // 测试2：下载速度功能验证
    document.getElementById('test-download').addEventListener('click', async () => {
      const button = document.getElementById('test-download');
      button.disabled = true;
      
      setStatus('download-status', '正在测试下载功能...', 'info');
      log('download-log', '=== 下载功能测试开始 ===');
      
      // 模拟下载测试
      const mockDownloadTest = async (prefix) => {
        log('download-log', `测试前缀: ${prefix}`);
        
        // 模拟不同的测试结果
        const scenarios = [
          { downloadSpeed: 25.67, debugInfo: '50000 bytes in 0.15s' },
          { downloadSpeed: 0, debugInfo: 'Duration: 0.02s, Bytes: 500' },
          { downloadSpeed: 0, error: 'Connection timeout', debugInfo: 'Error: TimeoutError - Connection timeout' },
          { downloadSpeed: 45.23, debugInfo: '75000 bytes in 0.13s' },
          { downloadSpeed: 0, debugInfo: 'Duration: 2.5s, Bytes: 800' }
        ];
        
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 200));
        
        const result = scenarios[Math.floor(Math.random() * scenarios.length)];
        
        if (result.downloadSpeed > 0) {
          log('download-log', `✓ 成功: ${result.downloadSpeed.toFixed(2)} Mbps (${result.debugInfo})`);
        } else if (result.error) {
          log('download-log', `✗ 错误: ${result.error} (${result.debugInfo})`);
        } else {
          log('download-log', `⚠ 无速度数据: ${result.debugInfo}`);
        }
        
        return result;
      };
      
      try {
        for (const item of testPrefixes) {
          await mockDownloadTest(item.prefix);
        }
        
        log('download-log', '=== 下载功能测试完成 ===');
        setStatus('download-status', '下载功能测试完成', 'success');
      } catch (error) {
        log('download-log', `错误: ${error.message}`);
        setStatus('download-status', '下载功能测试失败', 'error');
      }
      
      button.disabled = false;
    });

    // 清空日志
    document.getElementById('clear-log').addEventListener('click', () => {
      document.getElementById('concurrent-log').innerHTML = '';
      document.getElementById('download-log').innerHTML = '';
    });

    // 测试3：完整功能测试
    document.getElementById('test-full').addEventListener('click', async () => {
      const button = document.getElementById('test-full');
      button.disabled = true;
      
      const table = document.getElementById('results-table');
      const tbody = document.getElementById('results-body');
      
      table.style.display = 'table';
      tbody.innerHTML = '';
      
      setStatus('full-status', '正在进行完整测试...', 'info');
      
      // 创建表格行
      testPrefixes.forEach(item => {
        const row = tbody.insertRow();
        row.innerHTML = `
          <td>${item.provider}</td>
          <td>${item.region}</td>
          <td>${item.prefix}</td>
          <td>等待中...</td>
          <td>-</td>
          <td>-</td>
          <td>-</td>
        `;
      });
      
      const startTime = Date.now();
      let completedCount = 0;
      
      // 模拟完整测试流程
      const testPromises = testPrefixes.map(async (item, index) => {
        const row = tbody.rows[index];
        const cells = row.cells;
        
        const itemStartTime = Date.now();
        cells[6].textContent = new Date(itemStartTime).toLocaleTimeString();
        cells[3].textContent = '测试中...';
        
        try {
          // 模拟延迟测试
          await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 300));
          const latency = Math.floor(Math.random() * 100 + 20);
          
          // 模拟下载测试
          await new Promise(resolve => setTimeout(resolve, Math.random() * 1500 + 500));
          const downloadSpeed = Math.random() > 0.3 ? (Math.random() * 50 + 10).toFixed(2) : 0;
          
          cells[3].textContent = '成功';
          cells[3].style.color = 'green';
          cells[4].textContent = latency + ' ms';
          cells[5].textContent = downloadSpeed > 0 ? downloadSpeed + ' Mbps' : 'N/A';
          
        } catch (error) {
          cells[3].textContent = '失败';
          cells[3].style.color = 'red';
          cells[4].textContent = 'N/A';
          cells[5].textContent = 'N/A';
        }
        
        completedCount++;
        setStatus('full-status', `测试进度: ${completedCount}/${testPrefixes.length}`, 'info');
      });
      
      await Promise.all(testPromises);
      
      const totalTime = Date.now() - startTime;
      setStatus('full-status', `完整测试完成！总耗时: ${totalTime}ms`, 'success');
      
      button.disabled = false;
    });
  </script>
</body>
</html>
