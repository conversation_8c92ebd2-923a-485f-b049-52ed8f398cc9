// 简单的测试脚本来验证数据解析逻辑
// 这个脚本模拟 HTMLRewriter 的行为来测试解析逻辑

// 模拟的 HTML 数据（基于 nat64.xyz.html 的完整结构）
const mockHtmlData = [
  {
    provider: '<PERSON><PERSON>',
    country: 'Germany / Nürnberg',
    prefixes: '2a00:1098:2b::/96|BR|2a00:1098:2c:1::/96|BR|2a01:4f8:c2c:123f:64::/96|BR|2a01:4f9:c010:3f02:64::/96'
  },
  {
    provider: '<PERSON><PERSON>',
    country: 'United Kingdom / London',
    prefixes: '2a00:1098:2b::/96|BR|2a00:1098:2c:1::/96|BR|2a01:4f8:c2c:123f:64::/96|BR|2a01:4f9:c010:3f02:64::/96'
  },
  {
    provider: '<PERSON><PERSON>',
    country: 'United Kingdom / London', // 重复条目
    prefixes: '2a00:1098:2b::/96|BR|2a00:1098:2c:1::/96|BR|2a01:4f8:c2c:123f:64::/96|BR|2a01:4f9:c010:3f02:64::/96'
  },
  {
    provider: 'Kasper Dupont',
    country: 'Finland / Helsinki',
    prefixes: '2a00:1098:2b::/96|BR|2a00:1098:2c:1::/96|BR|2a01:4f8:c2c:123f:64::/96|BR|2a01:4f9:c010:3f02:64::/96'
  },
  {
    provider: '<a href=https://level66.services/services/nat64/>level66.services</a>',
    country: 'Germany / Anycast',
    prefixes: '2001:67c:2960:6464::/96'
  },
  {
    provider: 'Trex',
    country: 'Finland / Tampere',
    prefixes: '2001:67c:2b0:db32:0:1::/96'
  },
  {
    provider: '<a href=https://www.ztvi.org>ZTVI</a>',
    country: 'U.S.A. / Fremont',
    prefixes: '2602:fc59:b0:64::/96'
  },
  {
    provider: '<a href=https://www.ztvi.org>ZTVI</a>',
    country: 'U.S.A. / Chicago',
    prefixes: '2602:fc59:11:64::/96'
  }
];

// 模拟新的解析逻辑
function testParseLogic() {
  console.log('测试新的数据解析逻辑...\n');

  const providersMap = {};

  // 处理所有收集到的行数据，构建分层结构
  for (const row of mockHtmlData) {
    // 清理供应商名称，移除HTML标签
    const cleanProvider = row.provider.trim().replace(/<[^>]*>/g, '');
    const country = row.country.trim();

    // 分割前缀并过滤有效的 NAT64 前缀
    const prefixes = row.prefixes
      .split('|BR|')
      .map(p => p.trim())
      .filter(p => p && p.includes('::/96'));

    if (prefixes.length > 0) {
      // 如果供应商不存在，创建新条目
      if (!providersMap[cleanProvider]) {
        providersMap[cleanProvider] = {};
      }

      // 添加地区和前缀（如果地区已存在，会覆盖，这样可以去重）
      providersMap[cleanProvider][country] = prefixes;
    }
  }

  console.log('解析结果:');
  let totalPrefixes = 0;
  let providerIndex = 1;

  for (const [provider, regions] of Object.entries(providersMap)) {
    console.log(`${providerIndex}. 供应商: ${provider}`);

    let regionIndex = 1;
    for (const [region, prefixes] of Object.entries(regions)) {
      console.log(`   ${regionIndex}. 地区: ${region}`);
      console.log(`      前缀数量: ${prefixes.length}`);
      console.log(`      前缀列表: ${prefixes.join(', ')}`);
      totalPrefixes += prefixes.length;
      regionIndex++;
    }
    console.log('');
    providerIndex++;
  }

  const providerCount = Object.keys(providersMap).length;
  console.log(`总计: ${providerCount} 个提供商，${totalPrefixes} 个前缀`);

  return providersMap;
}

// 运行测试
testParseLogic();
