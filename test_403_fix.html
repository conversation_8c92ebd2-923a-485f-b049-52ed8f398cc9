<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HTTP 403 错误修复验证</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
    .container { max-width: 1000px; margin: auto; }
    .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    h1, h2 { color: #333; }
    .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
    .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; margin: 10px 0; }
    .code { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 4px; font-family: monospace; font-size: 12px; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #dee2e6; font-size: 12px; }
    thead { background: #343a40; color: white; }
    .highlight { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 10px 0; }
  </style>
</head>
<body>
  <div class="container">
    <h1>HTTP 403 Forbidden 错误修复方案</h1>
    
    <div class="test-section">
      <h2>问题分析</h2>
      <div class="error">
        <strong>当前错误：</strong><br>
        所有NAT64前缀测试都返回："Debug: Tele2 1MB test file: HTTP 403 Forbidden"
      </div>
      
      <div class="highlight">
        <strong>403错误的常见原因：</strong><br>
        • User-Agent被识别为机器人/爬虫<br>
        • 缺少必要的HTTP请求头<br>
        • 服务器地理位置或IP限制<br>
        • 反爬虫或DDoS防护机制
      </div>
    </div>
    
    <div class="test-section">
      <h2>修复方案1：改进HTTP请求头</h2>
      
      <div class="code">
        <strong>修复前（问题请求头）：</strong><br>
        headers: {<br>
        &nbsp;&nbsp;'Host': target.host,<br>
        &nbsp;&nbsp;'User-Agent': 'Cloudflare-Worker-NAT64-Test', // ❌ 明显的机器人标识<br>
        &nbsp;&nbsp;'Accept': '*/*',<br>
        &nbsp;&nbsp;'Connection': 'close'<br>
        }
      </div>
      
      <div class="code">
        <strong>修复后（真实浏览器请求头）：</strong><br>
        headers: {<br>
        &nbsp;&nbsp;'Host': target.host,<br>
        &nbsp;&nbsp;'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', // ✅ 真实浏览器<br>
        &nbsp;&nbsp;'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',<br>
        &nbsp;&nbsp;'Accept-Language': 'en-US,en;q=0.9',<br>
        &nbsp;&nbsp;'Accept-Encoding': 'gzip, deflate',<br>
        &nbsp;&nbsp;'Cache-Control': 'no-cache',<br>
        &nbsp;&nbsp;'Pragma': 'no-cache',<br>
        &nbsp;&nbsp;'Connection': 'keep-alive',<br>
        &nbsp;&nbsp;'Upgrade-Insecure-Requests': '1'<br>
        }
      </div>
    </div>
    
    <div class="test-section">
      <h2>修复方案2：多目标故障转移</h2>
      
      <table>
        <thead>
          <tr>
            <th>优先级</th>
            <th>服务器</th>
            <th>IP地址</th>
            <th>文件路径</th>
            <th>特点</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>1</td>
            <td>proof.ovh.net</td>
            <td>************</td>
            <td>/files/1Mio.dat</td>
            <td>OVH官方测速，通常更稳定</td>
          </tr>
          <tr>
            <td>2</td>
            <td>speedtest.tele2.net</td>
            <td>************</td>
            <td>/1MB.zip</td>
            <td>Tele2测速服务器</td>
          </tr>
          <tr>
            <td>3</td>
            <td>lg.hetzner.de</td>
            <td>**************</td>
            <td>/1MB.bin</td>
            <td>Hetzner Looking Glass</td>
          </tr>
          <tr>
            <td>4</td>
            <td>speedtest.wdc01.softlayer.com</td>
            <td>*************</td>
            <td>/downloads/test10.zip</td>
            <td>IBM Cloud测速服务器</td>
          </tr>
          <tr>
            <td>5</td>
            <td>bouygues.testdebit.info</td>
            <td>************</td>
            <td>/1M.iso</td>
            <td>Bouygues测速服务器</td>
          </tr>
        </tbody>
      </table>
      
      <div class="success">
        <strong>故障转移逻辑：</strong><br>
        • 按优先级顺序尝试每个服务器<br>
        • 如果遇到403错误，自动尝试下一个<br>
        • 只有所有服务器都失败才返回错误<br>
        • 增加成功率和可靠性
      </div>
    </div>
    
    <div class="test-section">
      <h2>修复方案3：放宽成功条件</h2>
      
      <div class="code">
        <strong>修复前：</strong><br>
        if (durationInSeconds >= 0.1 && contentBytes >= target.bytes * 0.3) // 需要30%数据
      </div>
      
      <div class="code">
        <strong>修复后：</strong><br>
        if (durationInSeconds >= 0.1 && contentBytes >= target.bytes * 0.2) // 只需要20%数据
      </div>
      
      <div class="highlight">
        <strong>原因：</strong>某些NAT64前缀可能网络质量较差，适当放宽条件可以获得更多有效数据
      </div>
    </div>
    
    <div class="test-section">
      <h2>预期修复效果</h2>
      
      <div class="success">
        <strong>修复前：</strong><br>
        所有测试结果：Debug: Tele2 1MB test file: HTTP 403 Forbidden
      </div>
      
      <div class="success">
        <strong>修复后：</strong><br>
        • 大部分前缀显示真实下载速度（如：25.67 Mbps）<br>
        • 少数前缀可能显示备用服务器结果<br>
        • 极少数前缀可能仍然失败（网络问题）<br>
        • 整体成功率应该大幅提升
      </div>
    </div>
    
    <div class="test-section">
      <h2>部署建议</h2>
      
      <div class="highlight">
        <strong>立即部署测试：</strong><br>
        1. 重新部署到Cloudflare Pages<br>
        2. 测试几个不同的NAT64前缀<br>
        3. 观察是否还有403错误<br>
        4. 检查是否获得真实的下载速度数据<br><br>
        
        <strong>如果仍有问题：</strong><br>
        • 可能需要进一步调整User-Agent<br>
        • 或者添加更多备用测速服务器<br>
        • 考虑使用HTTPS而不是HTTP
      </div>
    </div>
  </div>
</body>
</html>
